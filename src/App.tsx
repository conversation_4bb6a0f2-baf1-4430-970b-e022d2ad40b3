import React, { useState } from 'react';
import { Header } from './components/Header';
import { PrivacyNotice } from './components/PrivacyNotice';
import { SymptomInput } from './components/SymptomInput';
import { PHQ9Assessment } from './components/PHQ9Assessment';
import { GAD7Assessment } from './components/GAD7Assessment';
import { ResultsDashboard } from './components/ResultsDashboard';
import { calculatePHQ9Score, calculateGAD7Score, assessOverallRiskLevel, generateRecommendations } from './utils/scoring';
import { getResourcesByRiskLevel } from './utils/resources';
import { AssessmentResult, SymptomEntry } from './types';

type Step = 'input' | 'phq9' | 'gad7' | 'results';

function App() {
  const [currentStep, setCurrentStep] = useState<Step>('input');
  const [symptoms, setSymptoms] = useState<string[]>([]);
  const [phq9Responses, setPHQ9Responses] = useState<number[]>([]);
  const [gad7Responses, setGAD7Responses] = useState<number[]>([]);
  const [assessment, setAssessment] = useState<AssessmentResult | null>(null);

  const handleSymptomSubmit = (userSymptoms: string[], transcripts: string[]) => {
    setSymptoms(userSymptoms);
    setCurrentStep('phq9');
  };

  const handlePHQ9Complete = (responses: number[]) => {
    setPHQ9Responses(responses);
    setCurrentStep('gad7');
  };

  const handleGAD7Complete = (responses: number[]) => {
    setGAD7Responses(responses);
    
    // Calculate results
    const phq9Result = calculatePHQ9Score(phq9Responses);
    const gad7Result = calculateGAD7Score(responses);
    const riskLevel = assessOverallRiskLevel(phq9Result.score, gad7Result.score, symptoms);
    const recommendations = generateRecommendations(riskLevel, phq9Result.score, gad7Result.score);
    const resources = getResourcesByRiskLevel(riskLevel);

    const newAssessment: AssessmentResult = {
      id: Date.now().toString(),
      date: new Date(),
      phq9Score: phq9Result.score,
      gad7Score: gad7Result.score,
      symptoms: symptoms.map((symptom, index) => ({
        id: index.toString(),
        type: 'text',
        content: symptom,
        timestamp: new Date(),
        sentiment: 'neutral'
      })) as SymptomEntry[],
      riskLevel: riskLevel as any,
      recommendations,
      resources
    };

    setAssessment(newAssessment);
    setCurrentStep('results');
  };

  const handleNewAssessment = () => {
    setCurrentStep('input');
    setSymptoms([]);
    setPHQ9Responses([]);
    setGAD7Responses([]);
    setAssessment(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 font-['Inter']">
      <Header />
      
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentStep === 'input' && (
          <>
            <PrivacyNotice />
            <div className="bg-white rounded-xl shadow-lg p-8">
              <SymptomInput onSubmit={handleSymptomSubmit} />
            </div>
          </>
        )}

        {currentStep === 'phq9' && (
          <div className="bg-white rounded-xl shadow-lg p-8">
            <PHQ9Assessment
              onComplete={handlePHQ9Complete}
              onBack={() => setCurrentStep('input')}
            />
          </div>
        )}

        {currentStep === 'gad7' && (
          <div className="bg-white rounded-xl shadow-lg p-8">
            <GAD7Assessment
              onComplete={handleGAD7Complete}
              onBack={() => setCurrentStep('phq9')}
            />
          </div>
        )}

        {currentStep === 'results' && assessment && (
          <ResultsDashboard
            assessment={assessment}
            onNewAssessment={handleNewAssessment}
          />
        )}
      </main>

      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-gray-600 mb-2">
              MindScope is a community initiative for mental health awareness and early intervention.
            </p>
            <p className="text-sm text-gray-500">
              If you're in crisis, please contact emergency services immediately: <strong>911</strong> or <strong>988</strong>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;