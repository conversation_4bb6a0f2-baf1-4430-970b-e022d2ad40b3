import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { GAD7_QUESTIONS } from '../utils/scoring';

interface GAD7AssessmentProps {
  onComplete: (responses: number[]) => void;
  onBack: () => void;
}

export function GAD7Assessment({ onComplete, onBack }: GAD7AssessmentProps) {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [responses, setResponses] = useState<number[]>(new Array(GAD7_QUESTIONS.length).fill(-1));

  const scoreLabels = [
    'Not at all',
    'Several days',
    'More than half the days',
    'Nearly every day'
  ];

  const handleResponse = (score: number) => {
    const newResponses = [...responses];
    newResponses[currentQuestion] = score;
    setResponses(newResponses);

    if (currentQuestion < GAD7_QUESTIONS.length - 1) {
      setTimeout(() => setCurrentQuestion(currentQuestion + 1), 300);
    }
  };

  const canProceed = responses.every(response => response !== -1);
  const progress = ((currentQuestion + 1) / GAD7_QUESTIONS.length) * 100;

  return (
    <div className="space-y-8">
      <div>
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>Back</span>
          </button>
          <span className="text-sm text-gray-600">
            Question {currentQuestion + 1} of {GAD7_QUESTIONS.length}
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
          <div
            className="bg-green-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      <div className="text-center">
        <h2 className="text-xl font-bold text-gray-900 mb-2">GAD-7 Anxiety Screening</h2>
        <p className="text-gray-600 mb-8">
          Over the last 2 weeks, how often have you been bothered by the following?
        </p>
        
        <div className="bg-white rounded-xl shadow-lg p-8 max-w-2xl mx-auto">
          <h3 className="text-lg font-medium text-gray-900 mb-8 leading-relaxed">
            {GAD7_QUESTIONS[currentQuestion]}
          </h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {scoreLabels.map((label, index) => (
              <button
                key={index}
                onClick={() => handleResponse(index)}
                className={`p-4 rounded-lg border-2 transition-all text-left ${
                  responses[currentQuestion] === index
                    ? 'border-green-500 bg-green-50 text-green-900'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={`w-4 h-4 rounded-full border-2 ${
                      responses[currentQuestion] === index
                        ? 'border-green-500 bg-green-500'
                        : 'border-gray-300'
                    }`}
                  />
                  <span className="font-medium">{label}</span>
                </div>
                <p className="text-sm text-gray-500 ml-7">{index} points</p>
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <button
          onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
          disabled={currentQuestion === 0}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg ${
            currentQuestion === 0
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
          }`}
        >
          <ChevronLeft className="w-4 h-4" />
          <span>Previous</span>
        </button>

        {currentQuestion < GAD7_QUESTIONS.length - 1 ? (
          <button
            onClick={() => setCurrentQuestion(currentQuestion + 1)}
            disabled={responses[currentQuestion] === -1}
            className={`flex items-center space-x-2 px-6 py-2 rounded-lg ${
              responses[currentQuestion] !== -1
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }`}
          >
            <span>Next</span>
            <ChevronRight className="w-4 h-4" />
          </button>
        ) : (
          <button
            onClick={() => onComplete(responses)}
            disabled={!canProceed}
            className={`px-8 py-2 rounded-lg font-medium ${
              canProceed
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }`}
          >
            Complete Assessment
          </button>
        )}
      </div>
    </div>
  );
}