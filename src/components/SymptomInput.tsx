import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Type, Volume2, Trash2, Send } from 'lucide-react';
import { useVoiceRecording } from '../hooks/useVoiceRecording';

interface SymptomInputProps {
  onSubmit: (symptoms: string[], transcripts: string[]) => void;
}

export function SymptomInput({ onSubmit }: SymptomInputProps) {
  const [textSymptoms, setTextSymptoms] = useState('');
  const [inputMode, setInputMode] = useState<'text' | 'voice'>('text');
  const voiceRecording = useVoiceRecording();

  const handleSubmit = () => {
    const symptoms = [];
    const transcripts = [];
    
    if (textSymptoms.trim()) {
      symptoms.push(textSymptoms.trim());
    }
    
    if (voiceRecording.transcript) {
      symptoms.push(voiceRecording.transcript);
      transcripts.push(voiceRecording.transcript);
    }
    
    if (symptoms.length > 0) {
      onSubmit(symptoms, transcripts);
    }
  };

  const isReadyToSubmit = textSymptoms.trim() || voiceRecording.transcript;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">How are you feeling?</h2>
        <p className="text-gray-600">Share your symptoms and feelings in whatever way feels most comfortable</p>
      </div>

      {/* Input Mode Toggle */}
      <div className="flex justify-center">
        <div className="bg-gray-100 p-1 rounded-lg inline-flex">
          <button
            onClick={() => setInputMode('text')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
              inputMode === 'text'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Type className="w-4 h-4" />
            <span>Text</span>
          </button>
          <button
            onClick={() => setInputMode('voice')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
              inputMode === 'voice'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Mic className="w-4 h-4" />
            <span>Voice</span>
          </button>
        </div>
      </div>

      {/* Text Input */}
      {inputMode === 'text' && (
        <div>
          <label htmlFor="symptoms" className="block text-sm font-medium text-gray-700 mb-2">
            Describe how you've been feeling
          </label>
          <textarea
            id="symptoms"
            rows={6}
            value={textSymptoms}
            onChange={(e) => setTextSymptoms(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            placeholder="I've been feeling... Having trouble with... It's been difficult to..."
          />
        </div>
      )}

      {/* Voice Input */}
      {inputMode === 'voice' && (
        <div className="space-y-4">
          <div className="text-center">
            <button
              onClick={voiceRecording.isRecording ? voiceRecording.stopRecording : voiceRecording.startRecording}
              className={`inline-flex items-center justify-center w-20 h-20 rounded-full transition-all ${
                voiceRecording.isRecording
                  ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                  : 'bg-blue-500 hover:bg-blue-600'
              }`}
            >
              {voiceRecording.isRecording ? (
                <MicOff className="w-8 h-8 text-white" />
              ) : (
                <Mic className="w-8 h-8 text-white" />
              )}
            </button>
            <p className="text-sm text-gray-600 mt-2">
              {voiceRecording.isRecording ? 'Recording... Click to stop' : 'Click to start recording'}
            </p>
          </div>

          {voiceRecording.error && (
            <p className="text-red-600 text-sm text-center">{voiceRecording.error}</p>
          )}

          {voiceRecording.audioUrl && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Volume2 className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-900">Recording</span>
                </div>
                <button
                  onClick={voiceRecording.clearRecording}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
              <audio controls className="w-full mb-3">
                <source src={voiceRecording.audioUrl} type="audio/wav" />
                Your browser does not support audio playback.
              </audio>
              {voiceRecording.transcript && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Transcript:</p>
                  <p className="text-sm text-gray-600 italic">"{voiceRecording.transcript}"</p>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Submit Button */}
      <div className="flex justify-center">
        <button
          onClick={handleSubmit}
          disabled={!isReadyToSubmit}
          className={`flex items-center space-x-2 px-8 py-3 rounded-lg font-medium transition-all ${
            isReadyToSubmit
              ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl'
              : 'bg-gray-200 text-gray-400 cursor-not-allowed'
          }`}
        >
          <Send className="w-4 h-4" />
          <span>Continue to Assessment</span>
        </button>
      </div>
    </div>
  );
}