import React from 'react';
import { Shield, <PERSON>, Lock } from 'lucide-react';

export function PrivacyNotice() {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
      <div className="flex items-start space-x-3">
        <Shield className="w-6 h-6 text-blue-600 mt-1 flex-shrink-0" />
        <div>
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Your Privacy Matters</h3>
          <div className="space-y-2 text-sm text-blue-800">
            <div className="flex items-center space-x-2">
              <Eye className="w-4 h-4" />
              <span>No personal identification required</span>
            </div>
            <div className="flex items-center space-x-2">
              <Lock className="w-4 h-4" />
              <span>Data processed locally when possible</span>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4" />
              <span>You control what information is saved</span>
            </div>
          </div>
          <p className="text-xs text-blue-700 mt-3 italic">
            This tool provides screening guidance only and is not a substitute for professional medical advice.
          </p>
        </div>
      </div>
    </div>
  );
}