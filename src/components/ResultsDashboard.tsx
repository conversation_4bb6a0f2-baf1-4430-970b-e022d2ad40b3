import React from 'react';
import { Download, AlertTriangle, CheckCircle, XCircle, AlertCircle, Phone, Globe, MapPin } from 'lucide-react';
import { AssessmentResult } from '../types';
import { generateAssessmentReport } from '../utils/pdfGenerator';
import { format } from 'date-fns';

interface ResultsDashboardProps {
  assessment: AssessmentResult;
  onNewAssessment: () => void;
}

export function ResultsDashboard({ assessment, onNewAssessment }: ResultsDashboardProps) {
  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'crisis':
        return <XCircle className="w-6 h-6 text-red-500" />;
      case 'high':
        return <AlertTriangle className="w-6 h-6 text-orange-500" />;
      case 'moderate':
        return <AlertCircle className="w-6 h-6 text-yellow-500" />;
      default:
        return <CheckCircle className="w-6 h-6 text-green-500" />;
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'crisis':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'high':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'moderate':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      default:
        return 'bg-green-50 border-green-200 text-green-800';
    }
  };

  const handleDownloadReport = () => {
    generateAssessmentReport(assessment);
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Your Assessment Results</h2>
        <p className="text-gray-600">Generated on {format(assessment.date, 'PPP')}</p>
      </div>

      {/* Risk Level Alert */}
      {assessment.riskLevel === 'crisis' && (
        <div className="bg-red-50 border-2 border-red-200 rounded-xl p-6">
          <div className="flex items-center space-x-3 mb-4">
            <XCircle className="w-8 h-8 text-red-600" />
            <div>
              <h3 className="text-lg font-bold text-red-900">URGENT: Immediate Support Needed</h3>
              <p className="text-red-800">Please contact emergency services or a crisis hotline right away.</p>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-red-600" />
                <div>
                  <p className="font-semibold text-red-900">Emergency: 911</p>
                  <p className="text-sm text-red-700">Immediate emergency services</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-red-600" />
                <div>
                  <p className="font-semibold text-red-900">Crisis Line: 988</p>
                  <p className="text-sm text-red-700">24/7 suicide prevention lifeline</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Scores Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-blue-600">{assessment.phq9Score}</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">PHQ-9 Score</h3>
            <p className="text-sm text-gray-600">Depression Screening</p>
            <p className="text-xs text-gray-500 mt-1">Out of 27 total points</p>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-green-600">{assessment.gad7Score}</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">GAD-7 Score</h3>
            <p className="text-sm text-gray-600">Anxiety Screening</p>
            <p className="text-xs text-gray-500 mt-1">Out of 21 total points</p>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              {getRiskIcon(assessment.riskLevel)}
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Risk Level</h3>
            <p className={`text-sm font-medium px-3 py-1 rounded-full inline-block mt-2 ${getRiskColor(assessment.riskLevel)}`}>
              {assessment.riskLevel.toUpperCase()}
            </p>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Recommendations</h3>
        <div className="space-y-3">
          {assessment.recommendations.map((recommendation, index) => (
            <div key={index} className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <span className="text-xs font-semibold text-blue-600">{index + 1}</span>
              </div>
              <p className="text-gray-700">{recommendation}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Support Resources */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Support Resources</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {assessment.resources.map((resource) => (
            <div key={resource.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-semibold text-gray-900">{resource.name}</h4>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  resource.cost === 'free' ? 'bg-green-100 text-green-800' :
                  resource.cost === 'paid' ? 'bg-blue-100 text-blue-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {resource.cost}
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-3">{resource.description}</p>
              <div className="space-y-2">
                {resource.phone && (
                  <div className="flex items-center space-x-2 text-sm">
                    <Phone className="w-4 h-4 text-gray-500" />
                    <span className="text-blue-600 font-medium">{resource.phone}</span>
                  </div>
                )}
                {resource.website && (
                  <div className="flex items-center space-x-2 text-sm">
                    <Globe className="w-4 h-4 text-gray-500" />
                    <a href={resource.website} target="_blank" rel="noopener noreferrer" 
                       className="text-blue-600 hover:underline">
                      Visit Website
                    </a>
                  </div>
                )}
                {resource.address && (
                  <div className="flex items-center space-x-2 text-sm">
                    <MapPin className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600">{resource.address}</span>
                  </div>
                )}
                <p className="text-xs text-gray-500">Available: {resource.availability}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <button
          onClick={handleDownloadReport}
          className="flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Download className="w-4 h-4" />
          <span>Download PDF Report</span>
        </button>
        
        <button
          onClick={onNewAssessment}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Take New Assessment
        </button>
      </div>

      {/* Disclaimer */}
      <div className="bg-gray-50 rounded-lg p-4 text-center">
        <p className="text-sm text-gray-600">
          <strong>Important:</strong> This assessment is for screening purposes only and is not a medical diagnosis. 
          Please consult with a qualified healthcare professional for proper evaluation and treatment.
        </p>
      </div>
    </div>
  );
}