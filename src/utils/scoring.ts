export const PHQ9_QUESTIONS = [
  "Little interest or pleasure in doing things",
  "Feeling down, depressed, or hopeless",
  "Trouble falling or staying asleep, or sleeping too much",
  "Feeling tired or having little energy",
  "Poor appetite or overeating",
  "Feeling bad about yourself or that you are a failure or have let yourself or your family down",
  "Trouble concentrating on things, such as reading the newspaper or watching television",
  "Moving or speaking so slowly that other people could have noticed. Or the opposite being so fidgety or restless that you have been moving around a lot more than usual",
  "Thoughts that you would be better off dead, or of hurting yourself"
];

export const GAD7_QUESTIONS = [
  "Feeling nervous, anxious, or on edge",
  "Not being able to stop or control worrying",
  "Worrying too much about different things",
  "Trouble relaxing",
  "Being so restless that it is hard to sit still",
  "Becoming easily annoyed or irritable",
  "Feeling afraid, as if something awful might happen"
];

export const PHQ9_SCORE_RANGES = {
  minimal: { min: 0, max: 4, label: "Minimal depression", severity: "low" },
  mild: { min: 5, max: 9, label: "Mild depression", severity: "low" },
  moderate: { min: 10, max: 14, label: "Moderate depression", severity: "moderate" },
  moderatelySevere: { min: 15, max: 19, label: "Moderately severe depression", severity: "high" },
  severe: { min: 20, max: 27, label: "Severe depression", severity: "crisis" }
};

export const GAD7_SCORE_RANGES = {
  minimal: { min: 0, max: 4, label: "Minimal anxiety", severity: "low" },
  mild: { min: 5, max: 9, label: "Mild anxiety", severity: "low" },
  moderate: { min: 10, max: 14, label: "Moderate anxiety", severity: "moderate" },
  severe: { min: 15, max: 21, label: "Severe anxiety", severity: "high" }
};

export function calculatePHQ9Score(responses: number[]): { score: number; interpretation: any } {
  const score = responses.reduce((sum, response) => sum + response, 0);
  
  for (const [key, range] of Object.entries(PHQ9_SCORE_RANGES)) {
    if (score >= range.min && score <= range.max) {
      return { score, interpretation: range };
    }
  }
  
  return { score, interpretation: PHQ9_SCORE_RANGES.minimal };
}

export function calculateGAD7Score(responses: number[]): { score: number; interpretation: any } {
  const score = responses.reduce((sum, response) => sum + response, 0);
  
  for (const [key, range] of Object.entries(GAD7_SCORE_RANGES)) {
    if (score >= range.min && score <= range.max) {
      return { score, interpretation: range };
    }
  }
  
  return { score, interpretation: GAD7_SCORE_RANGES.minimal };
}

export function assessOverallRiskLevel(phq9Score: number, gad7Score: number, symptoms: string[]): 'low' | 'moderate' | 'high' | 'crisis' {
  // Crisis indicators
  const crisisKeywords = ['suicide', 'kill myself', 'end it all', 'not worth living', 'better off dead'];
  const hasCrisisIndicators = symptoms.some(symptom => 
    crisisKeywords.some(keyword => symptom.toLowerCase().includes(keyword))
  );
  
  if (hasCrisisIndicators || phq9Score >= 20) {
    return 'crisis';
  }
  
  if (phq9Score >= 15 || gad7Score >= 15) {
    return 'high';
  }
  
  if (phq9Score >= 10 || gad7Score >= 10) {
    return 'moderate';
  }
  
  return 'low';
}

export function generateRecommendations(riskLevel: string, phq9Score: number, gad7Score: number): string[] {
  const recommendations = [];
  
  switch (riskLevel) {
    case 'crisis':
      recommendations.push(
        "URGENT: Please contact emergency services (911) or a crisis hotline immediately",
        "Do not wait - seek immediate professional help",
        "Consider going to your nearest emergency room",
        "Reach out to a trusted friend or family member right now"
      );
      break;
      
    case 'high':
      recommendations.push(
        "Schedule an appointment with a mental health professional within the next week",
        "Consider contacting your doctor or a counseling center",
        "Reach out to trusted friends or family for support",
        "Look into therapy options in your area"
      );
      break;
      
    case 'moderate':
      recommendations.push(
        "Consider speaking with a counselor or therapist within the next 2-3 weeks",
        "Practice self-care activities like regular exercise, good sleep, and healthy eating",
        "Try mindfulness or meditation apps",
        "Consider joining a support group"
      );
      break;
      
    case 'low':
      recommendations.push(
        "Continue monitoring your mental health regularly",
        "Maintain healthy lifestyle habits",
        "Stay connected with friends and family",
        "Consider stress management techniques"
      );
      break;
  }
  
  return recommendations;
}