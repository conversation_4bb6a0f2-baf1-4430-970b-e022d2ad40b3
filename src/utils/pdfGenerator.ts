import jsPDF from 'jspdf';
import { AssessmentResult } from '../types';
import { format } from 'date-fns';

export function generateAssessmentReport(assessment: AssessmentResult): void {
  const doc = new jsPDF();
  
  // Header
  doc.setFontSize(20);
  doc.setTextColor(59, 130, 246); // Blue-600
  doc.text('🧠 MindScope Mental Health Report', 20, 30);
  
  // Date and disclaimer
  doc.setFontSize(10);
  doc.setTextColor(107, 114, 128); // Gray-500
  doc.text(`Generated: ${format(assessment.date, 'PPP')}`, 20, 40);
  doc.text('This is not a medical diagnosis. Please consult a healthcare professional.', 20, 50);
  
  // Scores section
  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.text('Assessment Scores', 20, 70);
  
  doc.setFontSize(12);
  doc.text(`PHQ-9 Depression Score: ${assessment.phq9Score}/27`, 30, 85);
  doc.text(`GAD-7 Anxiety Score: ${assessment.gad7Score}/21`, 30, 95);
  
  // Risk level
  doc.setFontSize(14);
  doc.text('Overall Risk Assessment', 20, 115);
  
  let riskColor: [number, number, number] = [107, 114, 128]; // Default gray
  let riskText = assessment.riskLevel.toUpperCase();
  
  switch (assessment.riskLevel) {
    case 'crisis':
      riskColor = [239, 68, 68]; // Red-500
      break;
    case 'high':
      riskColor = [245, 101, 101]; // Red-400
      break;
    case 'moderate':
      riskColor = [251, 146, 60]; // Orange-400
      break;
    case 'low':
      riskColor = [34, 197, 94]; // Green-500
      break;
  }
  
  doc.setFontSize(12);
  doc.setTextColor(...riskColor);
  doc.text(`Risk Level: ${riskText}`, 30, 130);
  
  // Recommendations
  doc.setFontSize(14);
  doc.setTextColor(0, 0, 0);
  doc.text('Recommendations', 20, 150);
  
  doc.setFontSize(10);
  let yPosition = 165;
  assessment.recommendations.forEach((rec, index) => {
    const lines = doc.splitTextToSize(`${index + 1}. ${rec}`, 170);
    doc.text(lines, 30, yPosition);
    yPosition += lines.length * 5 + 5;
  });
  
  // Resources
  if (yPosition < 250) {
    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.text('Support Resources', 20, yPosition + 10);
    
    yPosition += 25;
    doc.setFontSize(10);
    assessment.resources.slice(0, 3).forEach(resource => {
      doc.text(`• ${resource.name}`, 30, yPosition);
      if (resource.phone) {
        doc.text(`  Phone: ${resource.phone}`, 35, yPosition + 8);
      }
      if (resource.website) {
        doc.text(`  Website: ${resource.website}`, 35, yPosition + 16);
      }
      yPosition += 25;
    });
  }
  
  // Footer
  doc.setFontSize(8);
  doc.setTextColor(107, 114, 128);
  doc.text('MindScope - Community Mental Health Support', 20, 280);
  doc.text('Always consult with qualified healthcare professionals for medical advice.', 20, 290);
  
  // Save the PDF
  doc.save(`mindscope-report-${format(assessment.date, 'yyyy-MM-dd')}.pdf`);
}