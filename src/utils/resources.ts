import { SupportResource } from '../types';

export const MENTAL_HEALTH_RESOURCES: SupportResource[] = [
  {
    id: '1',
    name: 'National Suicide Prevention Lifeline',
    type: 'crisis',
    phone: '988',
    website: 'https://suicidepreventionlifeline.org',
    availability: '24/7',
    cost: 'free',
    description: 'Free and confidential support for people in distress and prevention resources.'
  },
  {
    id: '2',
    name: 'Crisis Text Line',
    type: 'crisis',
    phone: 'Text HOME to 741741',
    website: 'https://crisistextline.org',
    availability: '24/7',
    cost: 'free',
    description: 'Free crisis support via text message.'
  },
  {
    id: '3',
    name: 'SAMHSA National Helpline',
    type: 'counseling',
    phone: '**************',
    website: 'https://samhsa.gov',
    availability: '24/7',
    cost: 'free',
    description: 'Treatment referral and information service for mental health and substance use disorders.'
  },
  {
    id: '4',
    name: 'BetterHelp',
    type: 'online',
    website: 'https://betterhelp.com',
    availability: 'Varies by therapist',
    cost: 'paid',
    description: 'Online therapy platform with licensed therapists.'
  },
  {
    id: '5',
    name: 'NAMI Support Groups',
    type: 'support_group',
    website: 'https://nami.org/Support-Education/Support-Groups',
    availability: 'Weekly meetings',
    cost: 'free',
    description: 'Peer support groups for individuals and families affected by mental illness.'
  },
  {
    id: '6',
    name: 'Open Path Psychotherapy Collective',
    type: 'counseling',
    website: 'https://openpathcollective.org',
    availability: 'Varies by provider',
    cost: 'sliding_scale',
    description: 'Affordable therapy with fees ranging from $30-$60 per session.'
  }
];

export function getResourcesByRiskLevel(riskLevel: string): SupportResource[] {
  switch (riskLevel) {
    case 'crisis':
      return MENTAL_HEALTH_RESOURCES.filter(r => r.type === 'crisis');
    case 'high':
      return MENTAL_HEALTH_RESOURCES.filter(r => r.type === 'crisis' || r.type === 'counseling');
    case 'moderate':
      return MENTAL_HEALTH_RESOURCES.filter(r => r.type === 'counseling' || r.type === 'online');
    default:
      return MENTAL_HEALTH_RESOURCES.filter(r => r.type === 'support_group' || r.type === 'online');
  }
}