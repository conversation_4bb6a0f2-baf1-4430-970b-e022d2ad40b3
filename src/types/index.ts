export interface SymptomEntry {
  id: string;
  type: 'text' | 'voice';
  content: string;
  timestamp: Date;
  sentiment?: 'positive' | 'neutral' | 'negative';
  riskLevel?: 'low' | 'moderate' | 'high' | 'crisis';
}

export interface PHQ9Response {
  question: string;
  score: number;
}

export interface GAD7Response {
  question: string;
  score: number;
}

export interface AssessmentResult {
  id: string;
  date: Date;
  phq9Score: number;
  gad7Score: number;
  symptoms: SymptomEntry[];
  riskLevel: 'low' | 'moderate' | 'high' | 'crisis';
  recommendations: string[];
  resources: SupportResource[];
}

export interface SupportResource {
  id: string;
  name: string;
  type: 'crisis' | 'counseling' | 'online' | 'support_group';
  phone?: string;
  website?: string;
  address?: string;
  availability: string;
  cost: 'free' | 'paid' | 'sliding_scale';
  description: string;
}

export interface UserProfile {
  id: string;
  age?: number;
  location?: string;
  preferredLanguage: string;
  consentToSave: boolean;
  assessmentHistory: AssessmentResult[];
}